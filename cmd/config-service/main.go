package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
	"gorm.io/driver/postgres"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"

	"paas-platform/internal/config"
	"paas-platform/pkg/cache"
	"paas-platform/pkg/logger"
	"paas-platform/pkg/middleware"
)

// ConfigServiceConfig 配置服务配置
type ConfigServiceConfig struct {
	Port     int    `mapstructure:"port"`
	Database struct {
		Type     string `mapstructure:"type"`     // postgres, sqlite
		Host     string `mapstructure:"host"`
		Port     int    `mapstructure:"port"`
		User     string `mapstructure:"user"`
		Password string `mapstructure:"password"`
		DBName   string `mapstructure:"dbname"`
		SSLMode  string `mapstructure:"sslmode"`
		FilePath string `mapstructure:"filepath"` // for sqlite
	} `mapstructure:"database"`
	Redis struct {
		Addr     string `mapstructure:"addr"`
		Password string `mapstructure:"password"`
		DB       int    `mapstructure:"db"`
	} `mapstructure:"redis"`
	Encryption struct {
		Key string `mapstructure:"key"`
	} `mapstructure:"encryption"`
	Log struct {
		Level  string `mapstructure:"level"`
		Format string `mapstructure:"format"`
	} `mapstructure:"log"`
}

func main() {
	// 加载配置
	cfg, err := loadConfig()
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 初始化日志
	logger := logger.NewLogger(cfg.Log.Level, cfg.Log.Format)

	// 初始化数据库
	db, err := initDatabase(cfg)
	if err != nil {
		logger.Error("初始化数据库失败", "error", err)
		os.Exit(1)
	}

	// 初始化 Redis
	redisClient, err := cache.NewRedisClient(cfg.Redis.Addr, cfg.Redis.Password, cfg.Redis.DB)
	if err != nil {
		logger.Error("初始化 Redis 失败", "error", err)
		os.Exit(1)
	}

	// 初始化加密器
	encryptor := config.NewAESEncryptor(cfg.Encryption.Key)

	// 初始化配置服务
	configService := config.NewConfigService(db, encryptor, logger, config.ServiceConfig{
		CacheEnabled:      true,
		CacheTTL:          time.Hour,
		VersionLimit:      100,
		AuditEnabled:      true,
		ValidationEnabled: true,
	})

	// 初始化处理器
	handler := config.NewHandler(configService, logger)

	// 初始化 WebSocket 管理器
	wsManager := config.NewWebSocketManager(logger)

	// 初始化配置监听器
	watcher := config.NewConfigWatcher(redisClient, wsManager, logger)
	go watcher.Start()

	// 设置路由
	router := setupRouter(handler, wsManager, logger)

	// 启动服务器
	srv := &http.Server{
		Addr:    fmt.Sprintf(":%d", cfg.Port),
		Handler: router,
	}

	// 优雅关闭
	go func() {
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Error("服务器启动失败", "error", err)
		}
	}()

	logger.Info("配置服务启动成功", "port", cfg.Port)

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("正在关闭配置服务...")

	// 关闭服务
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := srv.Shutdown(ctx); err != nil {
		logger.Error("服务器关闭失败", "error", err)
	}

	watcher.Stop()
	logger.Info("配置服务已关闭")
}

// loadConfig 加载配置
func loadConfig() (*ConfigServiceConfig, error) {
	viper.SetConfigName("config-service")
	viper.SetConfigType("yaml")
	viper.AddConfigPath("./configs")
	viper.AddConfigPath(".")

	// 设置默认值
	viper.SetDefault("port", 8084)
	viper.SetDefault("database.type", "sqlite")
	viper.SetDefault("database.filepath", "./data/config-service.db")
	viper.SetDefault("redis.addr", "localhost:6379")
	viper.SetDefault("redis.db", 0)
	viper.SetDefault("log.level", "info")
	viper.SetDefault("log.format", "json")

	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, err
		}
	}

	var cfg ConfigServiceConfig
	if err := viper.Unmarshal(&cfg); err != nil {
		return nil, err
	}

	return &cfg, nil
}

// initDatabase 初始化数据库
func initDatabase(cfg *ConfigServiceConfig) (*gorm.DB, error) {
	var db *gorm.DB
	var err error

	switch cfg.Database.Type {
	case "postgres":
		dsn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%d sslmode=%s",
			cfg.Database.Host, cfg.Database.User, cfg.Database.Password,
			cfg.Database.DBName, cfg.Database.Port, cfg.Database.SSLMode)
		db, err = gorm.Open(postgres.Open(dsn), &gorm.Config{})
	case "sqlite":
		db, err = gorm.Open(sqlite.Open(cfg.Database.FilePath), &gorm.Config{})
	default:
		return nil, fmt.Errorf("不支持的数据库类型: %s", cfg.Database.Type)
	}

	if err != nil {
		return nil, err
	}

	// 自动迁移
	if err := db.AutoMigrate(
		&config.Config{},
		&config.ConfigVersion{},
		&config.Secret{},
		&config.ConfigTemplate{},
		&config.FeatureFlag{},
		&config.ConfigSet{},
		&config.ConfigDistribution{},
		&config.ConfigAudit{},
	); err != nil {
		return nil, fmt.Errorf("数据库迁移失败: %w", err)
	}

	return db, nil
}

// setupRouter 设置路由
func setupRouter(handler *config.Handler, wsManager *config.WebSocketManager, logger config.Logger) *gin.Engine {
	// 设置 Gin 模式
	gin.SetMode(gin.ReleaseMode)

	router := gin.New()

	// 中间件
	router.Use(middleware.Logger(logger))
	router.Use(middleware.Recovery(logger))
	router.Use(middleware.CORS())

	// 健康检查
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "healthy",
			"service":   "config-service",
			"timestamp": time.Now().Unix(),
		})
	})

	// API 路由组
	v1 := router.Group("/api/v1")
	{
		// 配置管理
		configs := v1.Group("/configs")
		{
			configs.POST("", handler.CreateConfig)           // 创建配置
			configs.GET("", handler.ListConfigs)             // 获取配置列表
			configs.GET("/:id", handler.GetConfig)           // 获取配置详情
			configs.PUT("/:id", handler.UpdateConfig)        // 更新配置
			configs.DELETE("/:id", handler.DeleteConfig)     // 删除配置

			// 配置查询
			configs.GET("/search", handler.SearchConfigs)    // 搜索配置
			configs.GET("/effective", handler.GetEffectiveConfig) // 获取有效配置

			// 批量操作
			configs.POST("/batch", handler.BatchUpdateConfigs) // 批量更新
			configs.GET("/batch", handler.BatchGetConfigs)     // 批量获取

			// 版本管理
			configs.GET("/:id/versions", handler.GetConfigVersions) // 获取版本历史
			configs.POST("/:id/rollback", handler.RollbackConfig)   // 回滚配置

			// 配置导入导出
			configs.POST("/export", handler.ExportConfigs)    // 导出配置
			configs.POST("/import", handler.ImportConfigs)    // 导入配置

			// 配置验证
			configs.POST("/validate", handler.ValidateConfig) // 验证配置
		}

		// 密钥管理
		secrets := v1.Group("/secrets")
		{
			secrets.POST("", handler.CreateSecret)           // 创建密钥
			secrets.GET("", handler.ListSecrets)             // 获取密钥列表
			secrets.GET("/:id", handler.GetSecret)           // 获取密钥详情
			secrets.PUT("/:id", handler.UpdateSecret)        // 更新密钥
			secrets.DELETE("/:id", handler.DeleteSecret)     // 删除密钥

			// 密钥轮换
			secrets.POST("/:id/rotate", handler.RotateSecret) // 轮换密钥
		}

		// 功能开关
		flags := v1.Group("/feature-flags")
		{
			flags.POST("", handler.CreateFeatureFlag)        // 创建功能开关
			flags.GET("", handler.ListFeatureFlags)          // 获取功能开关列表
			flags.GET("/:id", handler.GetFeatureFlag)        // 获取功能开关详情
			flags.PUT("/:id", handler.UpdateFeatureFlag)     // 更新功能开关
			flags.DELETE("/:id", handler.DeleteFeatureFlag)  // 删除功能开关

			// 功能开关评估
			flags.POST("/evaluate", handler.EvaluateFeatureFlag) // 评估功能开关
		}

		// 配置模板
		templates := v1.Group("/templates")
		{
			templates.POST("", handler.CreateTemplate)       // 创建模板
			templates.GET("", handler.ListTemplates)         // 获取模板列表
			templates.GET("/:id", handler.GetTemplate)       // 获取模板详情
			templates.PUT("/:id", handler.UpdateTemplate)    // 更新模板
			templates.DELETE("/:id", handler.DeleteTemplate) // 删除模板

			// 模板渲染
			templates.POST("/:id/render", handler.RenderTemplate) // 渲染模板
		}

		// 配置集
		sets := v1.Group("/config-sets")
		{
			sets.POST("", handler.CreateConfigSet)           // 创建配置集
			sets.GET("", handler.ListConfigSets)             // 获取配置集列表
			sets.GET("/:id", handler.GetConfigSet)           // 获取配置集详情
			sets.PUT("/:id", handler.UpdateConfigSet)        // 更新配置集
			sets.DELETE("/:id", handler.DeleteConfigSet)     // 删除配置集
		}

		// 配置分发
		distribution := v1.Group("/distribution")
		{
			distribution.POST("", handler.DistributeConfig)           // 分发配置
			distribution.GET("/:id/status", handler.GetDistributionStatus) // 获取分发状态
		}
	}

	// WebSocket 路由
	router.GET("/ws/configs", wsManager.HandleWebSocket)

	return router
}
