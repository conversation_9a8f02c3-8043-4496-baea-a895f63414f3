package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"paas-platform/internal/app"
	"paas-platform/internal/database"
	"paas-platform/pkg/logger"
	"paas-platform/pkg/middleware"

	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
	"github.com/swaggo/files"
	"github.com/swaggo/gin-swagger"

	_ "paas-platform/docs" // 导入生成的 docs 包
)

// @title PaaS 平台应用管理服务 API
// @version 1.0
// @description PaaS 平台的应用管理服务，提供应用生命周期管理功能
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.swagger.io/support
// @contact.email <EMAIL>

// @license.name MIT
// @license.url https://opensource.org/licenses/MIT

// @host localhost:8082
// @BasePath /api/v1

// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
// @description Type "Bearer" followed by a space and JWT token.

func main() {
	// 初始化配置
	initConfig()
	
	// 初始化日志
	logger := logger.NewLogger()
	
	// 初始化数据库
	db, err := database.NewConnection()
	if err != nil {
		log.Fatalf("数据库连接失败: %v", err)
	}
	
	// 自动迁移数据库表
	if err := database.AutoMigrate(db); err != nil {
		log.Fatalf("数据库迁移失败: %v", err)
	}
	
	// 初始化服务
	appService := app.NewAppService(db, logger)
	appHandler := app.NewHandler(appService, logger)
	
	// 初始化 Gin 路由
	router := setupRouter(appHandler, logger)
	
	// 启动 HTTP 服务器
	srv := &http.Server{
		Addr:    fmt.Sprintf(":%d", viper.GetInt("server.port")),
		Handler: router,
	}
	
	// 优雅启动
	go func() {
		logger.Info("应用管理服务启动", "port", viper.GetInt("server.port"))
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("服务启动失败: %v", err)
		}
	}()
	
	// 等待中断信号以优雅关闭服务器
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	
	logger.Info("正在关闭应用管理服务...")
	
	// 优雅关闭，超时时间为 30 秒
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	
	if err := srv.Shutdown(ctx); err != nil {
		log.Fatalf("服务关闭失败: %v", err)
	}
	
	logger.Info("应用管理服务已关闭")
}

// initConfig 初始化配置
func initConfig() {
	viper.SetConfigName("app-manager")
	viper.SetConfigType("yaml")
	viper.AddConfigPath("./configs")
	viper.AddConfigPath(".")
	
	// 设置默认值
	viper.SetDefault("server.port", 8081)
	viper.SetDefault("server.mode", "debug")
	viper.SetDefault("database.driver", "sqlite")
	viper.SetDefault("database.dsn", "./data/app-manager.db")
	viper.SetDefault("log.level", "info")
	viper.SetDefault("log.format", "json")
	
	// 支持环境变量
	viper.AutomaticEnv()
	viper.SetEnvPrefix("APP_MANAGER")
	
	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); ok {
			log.Println("配置文件未找到，使用默认配置")
		} else {
			log.Fatalf("读取配置文件失败: %v", err)
		}
	}
}

// setupRouter 设置路由
func setupRouter(appHandler *app.Handler, logger logger.Logger) *gin.Engine {
	// 设置 Gin 模式
	gin.SetMode(viper.GetString("server.mode"))
	
	router := gin.New()
	
	// 添加中间件
	router.Use(middleware.Logger(logger))
	router.Use(middleware.Recovery(logger))
	router.Use(middleware.CORS())
	
	// 健康检查接口
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "healthy",
			"service":   "app-manager",
			"version":   "1.0.0",
			"timestamp": time.Now().Format(time.RFC3339),
		})
	})
	
	// 就绪检查接口
	router.GET("/ready", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"ready":     true,
			"timestamp": time.Now().Format(time.RFC3339),
		})
	})
	
	// Swagger 文档 (开发环境) - 放在认证中间件之前
	if gin.Mode() == gin.DebugMode {
		// 集成 Swagger UI
		router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

		// 添加 API 文档重定向
		router.GET("/docs", func(c *gin.Context) {
			c.Redirect(http.StatusMovedPermanently, "/swagger/index.html")
		})

		// 添加 API 文档信息接口
		router.GET("/api/docs", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"title":       "PaaS 平台应用管理服务 API",
				"version":     "1.0.0",
				"description": "PaaS 平台的应用管理服务，提供应用生命周期管理功能",
				"swagger_url": "/swagger/index.html",
				"base_path":   "/api/v1",
				"host":        fmt.Sprintf("localhost:%d", viper.GetInt("server.port")),
			})
		})
	}

	// API 路由组
	v1 := router.Group("/api/v1")
	{
		// 添加认证中间件 (TODO: 实现具体的认证逻辑)
		v1.Use(middleware.Auth())

		// 注册应用管理路由
		appHandler.RegisterRoutes(v1)
	}
	
	return router
}
