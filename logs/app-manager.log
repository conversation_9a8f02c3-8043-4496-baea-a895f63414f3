[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

[GIN-debug] GET    /health                   --> main.setupRouter.func1 (4 handlers)
[GIN-debug] GET    /ready                    --> main.setupRouter.func2 (4 handlers)
[GIN-debug] POST   /api/v1/apps              --> paas-platform/internal/app.(*Handler).CreateApp-fm (5 handlers)
[GIN-debug] GET    /api/v1/apps              --> paas-platform/internal/app.(*Handler).ListApps-fm (5 handlers)
[GIN-debug] GET    /api/v1/apps/:id          --> paas-platform/internal/app.(*Handler).GetApp-fm (5 handlers)
[GIN-debug] PUT    /api/v1/apps/:id          --> paas-platform/internal/app.(*Handler).UpdateApp-fm (5 handlers)
[GIN-debug] DELETE /api/v1/apps/:id          --> paas-platform/internal/app.(*Handler).DeleteApp-fm (5 handlers)
[GIN-debug] POST   /api/v1/apps/:id/deploy   --> paas-platform/internal/app.(*Handler).DeployApp-fm (5 handlers)
[GIN-debug] POST   /api/v1/apps/:id/stop     --> paas-platform/internal/app.(*Handler).StopApp-fm (5 handlers)
[GIN-debug] POST   /api/v1/apps/:id/restart  --> paas-platform/internal/app.(*Handler).RestartApp-fm (5 handlers)
[GIN-debug] POST   /api/v1/apps/:id/scale    --> paas-platform/internal/app.(*Handler).ScaleApp-fm (5 handlers)
[GIN-debug] GET    /api/v1/apps/:id/versions --> paas-platform/internal/app.(*Handler).ListVersions-fm (5 handlers)
[GIN-debug] POST   /api/v1/apps/:id/versions --> paas-platform/internal/app.(*Handler).CreateVersion-fm (5 handlers)
[GIN-debug] POST   /api/v1/apps/:id/rollback/:version --> paas-platform/internal/app.(*Handler).RollbackVersion-fm (5 handlers)
[GIN-debug] GET    /api/v1/apps/:id/instances --> paas-platform/internal/app.(*Handler).ListInstances-fm (5 handlers)
[GIN-debug] GET    /api/v1/apps/:id/instances/:iid --> paas-platform/internal/app.(*Handler).GetInstance-fm (5 handlers)
[GIN-debug] GET    /api/v1/apps/:id/builds   --> paas-platform/internal/app.(*Handler).ListBuilds-fm (5 handlers)
[GIN-debug] POST   /api/v1/apps/:id/builds   --> paas-platform/internal/app.(*Handler).CreateBuild-fm (5 handlers)
[GIN-debug] GET    /api/v1/apps/:id/builds/:bid --> paas-platform/internal/app.(*Handler).GetBuild-fm (5 handlers)
[GIN-debug] GET    /api/v1/apps/:id/metrics  --> paas-platform/internal/app.(*Handler).GetMetrics-fm (5 handlers)
[GIN-debug] GET    /api/v1/apps/:id/logs     --> paas-platform/internal/app.(*Handler).GetLogs-fm (5 handlers)
[GIN-debug] GET    /swagger/*any             --> main.setupRouter.func3 (4 handlers)
{"timestamp":"2025-08-10T07:40:36Z","level":"INFO","service":"paas-platform","message":"应用管理服务启动","fields":{"port":8082}}
{"timestamp":"2025-08-10T07:40:39Z","level":"INFO","service":"paas-platform","message":"HTTP请求处理完成","fields":{"client_ip":"127.0.0.1","duration":"24.487µs","method":"GET","path":"/health","request_id":"bb8d29ad-b029-4402-8b4c-3b2a00d91135","status":200,"user_agent":"curl/7.88.1"}}
{"timestamp":"2025-08-10T07:40:39Z","level":"INFO","service":"paas-platform","message":"HTTP请求处理完成","fields":{"client_ip":"127.0.0.1","duration":"62.05µs","method":"GET","path":"/health","request_id":"b176be8c-84f4-480e-b3e0-424d4a9b0ac6","status":200,"user_agent":"curl/7.88.1"}}
{"timestamp":"2025-08-10T07:41:15Z","level":"INFO","service":"paas-platform","message":"正在关闭应用管理服务..."}
{"timestamp":"2025-08-10T07:41:15Z","level":"INFO","service":"paas-platform","message":"应用管理服务已关闭"}
