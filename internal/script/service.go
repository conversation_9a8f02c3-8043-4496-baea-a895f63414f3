package script

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"paas-platform/internal/container"
	"paas-platform/internal/runtime"
	"paas-platform/pkg/logger"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// ScriptExecutionService Python脚本执行服务接口
type ScriptExecutionService interface {
	// 提交执行任务
	SubmitTask(ctx context.Context, req *ExecutionRequest) (*Task, error)
	
	// 获取任务状态
	GetTaskStatus(ctx context.Context, taskID string) (*TaskStatus, error)
	
	// 取消任务
	CancelTask(ctx context.Context, taskID string) error
	
	// 获取任务结果
	GetTaskResult(ctx context.Context, taskID string) (*TaskResult, error)
	
	// 获取任务列表
	ListTasks(ctx context.Context, filter *TaskFilter) ([]*Task, int64, error)
	
	// 获取执行日志
	GetExecutionLogs(ctx context.Context, taskID string) ([]LogEntry, error)
	
	// 清理过期任务
	CleanupExpiredTasks(ctx context.Context, retentionDays int) error
}

// ExecutionRequest 执行请求
type ExecutionRequest struct {
	AppID         string                 `json:"app_id" validate:"required"`       // 应用ID
	ScriptPath    string                 `json:"script_path" validate:"required"`  // 脚本路径
	RuntimeType   string                 `json:"runtime_type"`                     // 运行时类型
	RuntimeConfig *RuntimeConfig         `json:"runtime_config"`                   // 运行时配置
	Parameters    map[string]interface{} `json:"parameters"`                       // 执行参数
	Environment   map[string]string      `json:"environment"`                      // 环境变量
	Timeout       time.Duration          `json:"timeout"`                          // 超时时间
	Priority      int                    `json:"priority"`                         // 优先级 (1-10)
	Callback      string                 `json:"callback"`                         // 回调URL
	UserID        string                 `json:"user_id"`                          // 用户ID
	TenantID      string                 `json:"tenant_id"`                        // 租户ID
}

// RuntimeConfig 运行时配置
type RuntimeConfig struct {
	Type         string                 `json:"type"`
	Version      string                 `json:"version"`
	ImageTag     string                 `json:"image_tag"`
	Dependencies *DependencyConfig      `json:"dependencies"`
	Resources    *ResourceLimits        `json:"resources"`
	Options      map[string]interface{} `json:"options"`
}

// DependencyConfig 依赖配置
type DependencyConfig struct {
	Type     string                 `json:"type"`
	File     string                 `json:"file"`
	Packages []string               `json:"packages"`
	Registry string                 `json:"registry"`
	Options  map[string]interface{} `json:"options"`
}

// ResourceLimits 资源限制
type ResourceLimits struct {
	CPULimit    string `json:"cpu_limit"`
	MemoryLimit string `json:"memory_limit"`
	DiskLimit   string `json:"disk_limit"`
}

// TaskFilter 任务过滤器
type TaskFilter struct {
	AppID    string     `json:"app_id"`
	UserID   string     `json:"user_id"`
	TenantID string     `json:"tenant_id"`
	Status   TaskStatus `json:"status"`
	Page     int        `json:"page"`
	PageSize int        `json:"page_size"`
}

// TaskStatus 任务状态响应
type TaskStatus struct {
	ID                  string    `json:"id"`
	Status              string    `json:"status"`
	Progress            int       `json:"progress"`
	StartTime           *time.Time `json:"start_time"`
	EstimatedCompletion *time.Time `json:"estimated_completion"`
	Message             string    `json:"message"`
}

// TaskResult 任务结果
type TaskResult struct {
	TaskID      string                 `json:"task_id"`
	Status      string                 `json:"status"`
	ExitCode    int                    `json:"exit_code"`
	Output      string                 `json:"output"`
	Error       string                 `json:"error"`
	Duration    int64                  `json:"duration"` // 毫秒
	StartTime   time.Time              `json:"start_time"`
	EndTime     time.Time              `json:"end_time"`
	Resources   ResourceUsage          `json:"resources"`
	Artifacts   []Artifact             `json:"artifacts"`
}

// LogEntry 日志条目
type LogEntry struct {
	Timestamp time.Time `json:"timestamp"`
	Level     string    `json:"level"`
	Message   string    `json:"message"`
	Source    string    `json:"source"`
}

// ResourceUsage 资源使用情况
type ResourceUsage struct {
	CPUUsage    float64 `json:"cpu_usage"`    // CPU使用率 (%)
	MemoryUsage int64   `json:"memory_usage"` // 内存使用量 (字节)
	DiskUsage   int64   `json:"disk_usage"`   // 磁盘使用量 (字节)
	NetworkRx   int64   `json:"network_rx"`   // 网络接收字节
	NetworkTx   int64   `json:"network_tx"`   // 网络发送字节
}

// Artifact 执行产物
type Artifact struct {
	Name     string `json:"name"`
	Path     string `json:"path"`
	Size     int64  `json:"size"`
	MimeType string `json:"mime_type"`
	URL      string `json:"url"`
}

// scriptExecutionService 脚本执行服务实现
type scriptExecutionService struct {
	db               *gorm.DB
	containerManager container.ContainerManager
	runtimeManager   runtime.RuntimeManager
	logger           logger.Logger
	config           ServiceConfig
}

// ServiceConfig 服务配置
type ServiceConfig struct {
	DefaultTimeout      time.Duration `json:"default_timeout"`
	MaxConcurrentTasks  int           `json:"max_concurrent_tasks"`
	TaskRetentionDays   int           `json:"task_retention_days"`
	DefaultPythonImage  string        `json:"default_python_image"`
	WorkspaceBasePath   string        `json:"workspace_base_path"`
	ArtifactBasePath    string        `json:"artifact_base_path"`
	CallbackTimeout     time.Duration `json:"callback_timeout"`
}

// NewScriptExecutionService 创建脚本执行服务
func NewScriptExecutionService(
	db *gorm.DB,
	containerManager container.ContainerManager,
	runtimeManager runtime.RuntimeManager,
	logger logger.Logger,
	config ServiceConfig,
) ScriptExecutionService {
	return &scriptExecutionService{
		db:               db,
		containerManager: containerManager,
		runtimeManager:   runtimeManager,
		logger:           logger,
		config:           config,
	}
}

// SubmitTask 提交执行任务
func (s *scriptExecutionService) SubmitTask(ctx context.Context, req *ExecutionRequest) (*Task, error) {
	// 验证请求参数
	if err := s.validateRequest(req); err != nil {
		return nil, fmt.Errorf("请求参数验证失败: %w", err)
	}

	// 检查并发任务限制
	if err := s.checkConcurrentLimit(ctx, req.TenantID); err != nil {
		return nil, fmt.Errorf("并发任务限制: %w", err)
	}

	// 创建任务
	task := &Task{
		ID:          uuid.New().String(),
		AppID:       req.AppID,
		UserID:      req.UserID,
		TenantID:    req.TenantID,
		ScriptPath:  req.ScriptPath,
		Parameters:  s.marshalJSON(req.Parameters),
		Environment: s.marshalJSON(req.Environment),
		Status:      TaskStatusPending,
		Priority:    req.Priority,
		Timeout:     int(req.Timeout.Seconds()),
		Callback:    req.Callback,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// 设置默认值
	if task.Timeout == 0 {
		task.Timeout = int(s.config.DefaultTimeout.Seconds())
	}
	if task.Priority == 0 {
		task.Priority = 5 // 默认优先级
	}

	// 保存任务到数据库
	if err := s.db.Create(task).Error; err != nil {
		return nil, fmt.Errorf("保存任务失败: %w", err)
	}

	// 异步执行任务
	go s.executeTaskAsync(context.Background(), task)

	s.logger.Info("脚本执行任务已提交", "task_id", task.ID, "app_id", req.AppID, "script_path", req.ScriptPath)
	return task, nil
}

// GetTaskStatus 获取任务状态
func (s *scriptExecutionService) GetTaskStatus(ctx context.Context, taskID string) (*TaskStatus, error) {
	var task Task
	if err := s.db.Where("id = ?", taskID).First(&task).Error; err != nil {
		return nil, fmt.Errorf("任务不存在: %w", err)
	}

	status := &TaskStatus{
		ID:      task.ID,
		Status:  string(task.Status),
		Message: task.Error,
	}

	// 计算进度
	if task.Status == TaskStatusRunning {
		status.Progress = s.calculateProgress(&task)
		status.EstimatedCompletion = s.estimateCompletion(&task)
	}

	if task.StartTime != nil {
		status.StartTime = task.StartTime
	}

	return status, nil
}

// CancelTask 取消任务
func (s *scriptExecutionService) CancelTask(ctx context.Context, taskID string) error {
	var task Task
	if err := s.db.Where("id = ?", taskID).First(&task).Error; err != nil {
		return fmt.Errorf("任务不存在: %w", err)
	}

	// 只能取消待执行或运行中的任务
	if task.Status != TaskStatusPending && task.Status != TaskStatusQueued && task.Status != TaskStatusRunning {
		return fmt.Errorf("任务状态不允许取消: %s", task.Status)
	}

	// 如果任务正在运行，停止容器
	if task.ContainerID != "" {
		if err := s.containerManager.DestroyContainer(ctx, task.ContainerID); err != nil {
			s.logger.Error("停止容器失败", "error", err, "container_id", task.ContainerID)
		}
	}

	// 更新任务状态
	endTime := time.Now()
	updates := map[string]interface{}{
		"status":     TaskStatusCanceled,
		"end_time":   &endTime,
		"updated_at": time.Now(),
	}

	if err := s.db.Model(&task).Updates(updates).Error; err != nil {
		return fmt.Errorf("更新任务状态失败: %w", err)
	}

	s.logger.Info("任务已取消", "task_id", taskID)
	return nil
}

// GetTaskResult 获取任务结果
func (s *scriptExecutionService) GetTaskResult(ctx context.Context, taskID string) (*TaskResult, error) {
	var task Task
	if err := s.db.Where("id = ?", taskID).First(&task).Error; err != nil {
		return nil, fmt.Errorf("任务不存在: %w", err)
	}

	result := &TaskResult{
		TaskID:   task.ID,
		Status:   string(task.Status),
		Output:   task.Output,
		Error:    task.Error,
		StartTime: time.Time{},
		EndTime:   time.Time{},
	}

	if task.ExitCode != nil {
		result.ExitCode = *task.ExitCode
	}

	if task.StartTime != nil {
		result.StartTime = *task.StartTime
	}

	if task.EndTime != nil {
		result.EndTime = *task.EndTime
		if !task.StartTime.IsZero() {
			result.Duration = task.EndTime.Sub(*task.StartTime).Milliseconds()
		}
	}

	// 解析资源使用情况
	if task.Resources != nil {
		var resources ResourceUsage
		if err := json.Unmarshal(task.Resources, &resources); err == nil {
			result.Resources = resources
		}
	}

	// 获取产物列表
	artifacts, err := s.getTaskArtifacts(ctx, taskID)
	if err != nil {
		s.logger.Error("获取任务产物失败", "error", err, "task_id", taskID)
	} else {
		result.Artifacts = artifacts
	}

	return result, nil
}

// ListTasks 获取任务列表
func (s *scriptExecutionService) ListTasks(ctx context.Context, filter *TaskFilter) ([]*Task, int64, error) {
	query := s.db.Model(&Task{})

	// 应用过滤条件
	if filter.AppID != "" {
		query = query.Where("app_id = ?", filter.AppID)
	}
	if filter.UserID != "" {
		query = query.Where("user_id = ?", filter.UserID)
	}
	if filter.TenantID != "" {
		query = query.Where("tenant_id = ?", filter.TenantID)
	}
	if filter.Status != "" {
		query = query.Where("status = ?", filter.Status)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("获取任务总数失败: %w", err)
	}

	// 分页查询
	var tasks []*Task
	offset := (filter.Page - 1) * filter.PageSize
	if err := query.Order("created_at DESC").Offset(offset).Limit(filter.PageSize).Find(&tasks).Error; err != nil {
		return nil, 0, fmt.Errorf("查询任务列表失败: %w", err)
	}

	return tasks, total, nil
}

// GetExecutionLogs 获取执行日志
func (s *scriptExecutionService) GetExecutionLogs(ctx context.Context, taskID string) ([]LogEntry, error) {
	var task Task
	if err := s.db.Where("id = ?", taskID).First(&task).Error; err != nil {
		return nil, fmt.Errorf("任务不存在: %w", err)
	}

	var logs []LogEntry

	// 如果容器存在，获取容器日志
	if task.ContainerID != "" {
		containerLogs, err := s.containerManager.GetContainerLogs(ctx, task.ContainerID)
		if err != nil {
			s.logger.Error("获取容器日志失败", "error", err, "container_id", task.ContainerID)
		} else {
			// 解析容器日志
			logs = s.parseContainerLogs(containerLogs)
		}
	}

	// 添加任务状态变更日志
	statusLogs := s.getTaskStatusLogs(ctx, taskID)
	logs = append(logs, statusLogs...)

	return logs, nil
}

// CleanupExpiredTasks 清理过期任务
func (s *scriptExecutionService) CleanupExpiredTasks(ctx context.Context, retentionDays int) error {
	expiredTime := time.Now().AddDate(0, 0, -retentionDays)

	// 查找过期任务
	var expiredTasks []Task
	if err := s.db.Where("created_at < ? AND status IN ?", expiredTime, 
		[]TaskStatus{TaskStatusCompleted, TaskStatusFailed, TaskStatusCanceled}).
		Find(&expiredTasks).Error; err != nil {
		return fmt.Errorf("查询过期任务失败: %w", err)
	}

	// 清理过期任务
	for _, task := range expiredTasks {
		// 清理任务产物
		if err := s.cleanupTaskArtifacts(ctx, task.ID); err != nil {
			s.logger.Error("清理任务产物失败", "error", err, "task_id", task.ID)
		}

		// 删除任务记录
		if err := s.db.Delete(&task).Error; err != nil {
			s.logger.Error("删除过期任务失败", "error", err, "task_id", task.ID)
		}
	}

	s.logger.Info("过期任务清理完成", "count", len(expiredTasks), "retention_days", retentionDays)
	return nil
}

// 私有方法

// executeTaskAsync 异步执行任务
func (s *scriptExecutionService) executeTaskAsync(ctx context.Context, task *Task) {
	// 更新任务状态为排队中
	s.updateTaskStatus(task.ID, TaskStatusQueued, "")

	// 执行任务
	if err := s.executeTask(ctx, task); err != nil {
		s.logger.Error("任务执行失败", "error", err, "task_id", task.ID)
		s.updateTaskStatus(task.ID, TaskStatusFailed, err.Error())
	}
}

// executeTask 执行任务
func (s *scriptExecutionService) executeTask(ctx context.Context, task *Task) error {
	// 更新任务状态为运行中
	startTime := time.Now()
	s.updateTaskStatus(task.ID, TaskStatusRunning, "")
	s.db.Model(task).Update("start_time", &startTime)

	// 创建执行容器
	containerSpec := s.buildContainerSpec(task)
	container, err := s.containerManager.CreateContainer(ctx, containerSpec)
	if err != nil {
		return fmt.Errorf("创建容器失败: %w", err)
	}

	// 更新容器ID
	s.db.Model(task).Update("container_id", container.ID)

	// 启动容器
	if err := s.containerManager.StartContainer(ctx, container.ID); err != nil {
		s.containerManager.DestroyContainer(ctx, container.ID)
		return fmt.Errorf("启动容器失败: %w", err)
	}

	// 执行Python脚本
	result, err := s.executeScript(ctx, container.ID, task)
	if err != nil {
		s.containerManager.DestroyContainer(ctx, container.ID)
		return fmt.Errorf("执行脚本失败: %w", err)
	}

	// 收集执行结果
	if err := s.collectExecutionResult(ctx, task, result); err != nil {
		s.logger.Error("收集执行结果失败", "error", err, "task_id", task.ID)
	}

	// 销毁容器
	if err := s.containerManager.DestroyContainer(ctx, container.ID); err != nil {
		s.logger.Error("销毁容器失败", "error", err, "container_id", container.ID)
	}

	// 更新任务状态
	endTime := time.Now()
	status := TaskStatusCompleted
	if result.ExitCode != 0 {
		status = TaskStatusFailed
	}

	updates := map[string]interface{}{
		"status":     status,
		"end_time":   &endTime,
		"exit_code":  result.ExitCode,
		"output":     result.Output,
		"error":      result.Error,
		"updated_at": time.Now(),
	}

	s.db.Model(task).Updates(updates)

	// 发送回调通知
	if task.Callback != "" {
		go s.sendCallback(task, result)
	}

	s.logger.Info("任务执行完成", "task_id", task.ID, "status", status, "exit_code", result.ExitCode)
	return nil
}

// 辅助方法

// validateRequest 验证请求参数
func (s *scriptExecutionService) validateRequest(req *ExecutionRequest) error {
	if req.AppID == "" {
		return fmt.Errorf("应用ID不能为空")
	}
	if req.ScriptPath == "" {
		return fmt.Errorf("脚本路径不能为空")
	}
	if req.Priority < 1 || req.Priority > 10 {
		return fmt.Errorf("优先级必须在1-10之间")
	}
	return nil
}

// checkConcurrentLimit 检查并发限制
func (s *scriptExecutionService) checkConcurrentLimit(ctx context.Context, tenantID string) error {
	var count int64
	err := s.db.Model(&Task{}).Where("tenant_id = ? AND status IN ?", tenantID, 
		[]TaskStatus{TaskStatusPending, TaskStatusQueued, TaskStatusRunning}).Count(&count).Error
	if err != nil {
		return fmt.Errorf("检查并发任务数失败: %w", err)
	}

	if int(count) >= s.config.MaxConcurrentTasks {
		return fmt.Errorf("超过最大并发任务数限制: %d", s.config.MaxConcurrentTasks)
	}

	return nil
}

// marshalJSON 序列化JSON
func (s *scriptExecutionService) marshalJSON(data interface{}) []byte {
	if data == nil {
		return nil
	}
	bytes, _ := json.Marshal(data)
	return bytes
}

// updateTaskStatus 更新任务状态
func (s *scriptExecutionService) updateTaskStatus(taskID string, status TaskStatus, errorMsg string) {
	updates := map[string]interface{}{
		"status":     status,
		"updated_at": time.Now(),
	}
	if errorMsg != "" {
		updates["error"] = errorMsg
	}
	s.db.Model(&Task{}).Where("id = ?", taskID).Updates(updates)
}

// buildContainerSpec 构建容器规格
func (s *scriptExecutionService) buildContainerSpec(task *Task) *container.ContainerSpec {
	// 解析环境变量
	env := make(map[string]string)
	if task.Environment != nil {
		json.Unmarshal(task.Environment, &env)
	}

	// 添加默认环境变量
	env["TASK_ID"] = task.ID
	env["SCRIPT_PATH"] = task.ScriptPath
	env["PYTHONUNBUFFERED"] = "1"

	// 解析执行参数
	var parameters map[string]interface{}
	if task.Parameters != nil {
		json.Unmarshal(task.Parameters, &parameters)
	}

	// 构建执行命令
	command := []string{"python", task.ScriptPath}

	// 添加参数到命令行
	for key, value := range parameters {
		command = append(command, fmt.Sprintf("--%s", key))
		command = append(command, fmt.Sprintf("%v", value))
	}

	return &container.ContainerSpec{
		ID:          task.ID,
		ImageTag:    s.config.DefaultPythonImage,
		Command:     command,
		WorkingDir:  "/workspace",
		Environment: env,
		Resources: container.ResourceLimits{
			CPULimit:    1000000000, // 1 CPU
			MemoryLimit: 512 * 1024 * 1024, // 512MB
			PidsLimit:   100,
		},
		Volumes: []container.VolumeMount{
			{
				Source:   fmt.Sprintf("%s/%s", s.config.WorkspaceBasePath, task.AppID),
				Target:   "/workspace",
				ReadOnly: true,
			},
			{
				Source:   fmt.Sprintf("%s/%s", s.config.ArtifactBasePath, task.ID),
				Target:   "/output",
				ReadOnly: false,
			},
		},
		NetworkMode: "bridge",
		Timeout:     time.Duration(task.Timeout) * time.Second,
		AutoRemove:  true,
	}
}

// executeScript 执行Python脚本
func (s *scriptExecutionService) executeScript(ctx context.Context, containerID string, task *Task) (*container.ExecutionResult, error) {
	// 设置超时上下文
	execCtx, cancel := context.WithTimeout(ctx, time.Duration(task.Timeout)*time.Second)
	defer cancel()

	// 构建执行命令
	var parameters map[string]interface{}
	if task.Parameters != nil {
		json.Unmarshal(task.Parameters, &parameters)
	}

	command := []string{"python", task.ScriptPath}
	for key, value := range parameters {
		command = append(command, fmt.Sprintf("--%s", key))
		command = append(command, fmt.Sprintf("%v", value))
	}

	// 执行命令
	result, err := s.containerManager.ExecuteCommand(execCtx, containerID, command)
	if err != nil {
		return nil, fmt.Errorf("执行命令失败: %w", err)
	}

	return result, nil
}

// collectExecutionResult 收集执行结果
func (s *scriptExecutionService) collectExecutionResult(ctx context.Context, task *Task, result *container.ExecutionResult) error {
	// 收集资源使用情况
	resourceUsage := ResourceUsage{
		CPUUsage:    0, // TODO: 从容器指标获取
		MemoryUsage: 0, // TODO: 从容器指标获取
	}

	resourceBytes, _ := json.Marshal(resourceUsage)
	s.db.Model(task).Update("resources", resourceBytes)

	// 收集输出产物
	artifacts, err := s.collectArtifacts(ctx, task.ID)
	if err != nil {
		s.logger.Error("收集产物失败", "error", err, "task_id", task.ID)
	} else {
		// 保存产物信息
		for _, artifact := range artifacts {
			artifactRecord := &TaskArtifact{
				ID:       uuid.New().String(),
				TaskID:   task.ID,
				Name:     artifact.Name,
				Path:     artifact.Path,
				Size:     artifact.Size,
				MimeType: artifact.MimeType,
				URL:      artifact.URL,
				CreatedAt: time.Now(),
			}
			s.db.Create(artifactRecord)
		}
	}

	return nil
}

// collectArtifacts 收集产物
func (s *scriptExecutionService) collectArtifacts(ctx context.Context, taskID string) ([]Artifact, error) {
	// TODO: 实现产物收集逻辑
	// 1. 扫描输出目录
	// 2. 上传到存储服务
	// 3. 生成访问URL
	return []Artifact{}, nil
}

// calculateProgress 计算进度
func (s *scriptExecutionService) calculateProgress(task *Task) int {
	if task.StartTime == nil {
		return 0
	}

	elapsed := time.Since(*task.StartTime)
	timeout := time.Duration(task.Timeout) * time.Second

	progress := int(float64(elapsed) / float64(timeout) * 100)
	if progress > 95 {
		progress = 95 // 最多显示95%，避免超时前显示100%
	}

	return progress
}

// estimateCompletion 估算完成时间
func (s *scriptExecutionService) estimateCompletion(task *Task) *time.Time {
	if task.StartTime == nil {
		return nil
	}

	timeout := time.Duration(task.Timeout) * time.Second
	completion := task.StartTime.Add(timeout)
	return &completion
}

// parseContainerLogs 解析容器日志
func (s *scriptExecutionService) parseContainerLogs(logs string) []LogEntry {
	var entries []LogEntry

	// TODO: 实现日志解析逻辑
	// 1. 按行分割日志
	// 2. 解析时间戳和日志级别
	// 3. 构造LogEntry对象

	return entries
}

// getTaskStatusLogs 获取任务状态变更日志
func (s *scriptExecutionService) getTaskStatusLogs(ctx context.Context, taskID string) []LogEntry {
	var logs []LogEntry

	// TODO: 从任务事件表获取状态变更记录

	return logs
}

// getTaskArtifacts 获取任务产物
func (s *scriptExecutionService) getTaskArtifacts(ctx context.Context, taskID string) ([]Artifact, error) {
	var artifacts []TaskArtifact
	if err := s.db.Where("task_id = ?", taskID).Find(&artifacts).Error; err != nil {
		return nil, fmt.Errorf("查询任务产物失败: %w", err)
	}

	var result []Artifact
	for _, artifact := range artifacts {
		result = append(result, Artifact{
			Name:     artifact.Name,
			Path:     artifact.Path,
			Size:     artifact.Size,
			MimeType: artifact.MimeType,
			URL:      artifact.URL,
		})
	}

	return result, nil
}

// cleanupTaskArtifacts 清理任务产物
func (s *scriptExecutionService) cleanupTaskArtifacts(ctx context.Context, taskID string) error {
	// 删除产物记录
	if err := s.db.Where("task_id = ?", taskID).Delete(&TaskArtifact{}).Error; err != nil {
		return fmt.Errorf("删除产物记录失败: %w", err)
	}

	// TODO: 删除实际文件
	// 1. 获取产物文件路径
	// 2. 从存储服务删除文件

	return nil
}

// sendCallback 发送回调通知
func (s *scriptExecutionService) sendCallback(task *Task, result *container.ExecutionResult) {
	// TODO: 实现回调通知逻辑
	// 1. 构造回调数据
	// 2. 发送HTTP请求
	// 3. 处理重试逻辑

	s.logger.Info("发送回调通知", "task_id", task.ID, "callback", task.Callback)
}
