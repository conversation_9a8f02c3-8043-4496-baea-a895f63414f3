package runtime

import (
	"context"
	"fmt"
	"strings"

	"paas-platform/internal/container"
	"paas-platform/pkg/logger"
)

// PythonAdapter Python运行时适配器
type PythonAdapter struct {
	*BaseRuntimeAdapter
}

// NewPythonAdapter 创建Python适配器
func NewPythonAdapter(logger logger.Logger) RuntimeAdapter {
	info := RuntimeInfo{
		Type:        RuntimeTypePython,
		Name:        "Python",
		Version:     "3.11.4",
		Description: "Python编程语言运行时环境",
		ImageTag:    "python:3.11.4-alpine",
		Extensions:  []string{".py", ".pyw"},
		Commands:    []string{"python", "python3", "pip", "pip3"},
		Supported:   true,
	}

	return &PythonAdapter{
		BaseRuntimeAdapter: NewBaseRuntimeAdapter(logger, info),
	}
}

// BuildContainerSpec 构建容器规格
func (pa *PythonAdapter) BuildContainerSpec(ctx context.Context, req *ExecutionRequest) (*container.ContainerSpec, error) {
	// 获取运行时配置
	config := req.RuntimeConfig
	if config == nil {
		config = &RuntimeConfig{
			Type:     RuntimeTypePython,
			ImageTag: pa.info.ImageTag,
		}
	}

	// 解析资源限制
	resourceLimits, err := ParseResourceLimits(config.Resources)
	if err != nil {
		return nil, fmt.Errorf("解析资源限制失败: %w", err)
	}

	// 构建环境变量
	environment := pa.buildEnvironment(req, config)

	// 构建执行命令
	command, err := pa.BuildExecutionCommand(req)
	if err != nil {
		return nil, fmt.Errorf("构建执行命令失败: %w", err)
	}

	// 构建容器规格
	spec := &container.ContainerSpec{
		ImageTag:    config.ImageTag,
		Command:     command,
		WorkingDir:  req.WorkingDir,
		Environment: environment,
		Resources:   resourceLimits,
		Volumes: []container.VolumeMount{
			{
				Source:   req.WorkingDir,
				Target:   "/workspace",
				ReadOnly: false,
			},
		},
		NetworkMode: "bridge",
		Timeout:     req.Timeout,
		AutoRemove:  true,
	}

	return spec, nil
}

// BuildExecutionCommand 构建执行命令
func (pa *PythonAdapter) BuildExecutionCommand(req *ExecutionRequest) ([]string, error) {
	var command []string

	// 检查是否需要安装依赖
	if req.RuntimeConfig != nil && req.RuntimeConfig.Dependencies != nil {
		command = append(command, pa.buildDependencyInstallCommand(req.RuntimeConfig.Dependencies)...)
		command = append(command, "&&")
	}

	// 基础Python执行命令
	command = append(command, "python")

	// 添加Python特定选项
	if req.RuntimeConfig != nil && req.RuntimeConfig.Options != nil {
		pythonOptions := pa.buildPythonOptions(req.RuntimeConfig.Options)
		command = append(command, pythonOptions...)
	}

	// 添加脚本路径
	command = append(command, req.ScriptPath)

	// 添加参数
	args := BuildParameterArgs(req.Parameters, ParameterStyleDoubleDash)
	command = append(command, args...)

	return command, nil
}

// HandleDependencies 处理依赖安装
func (pa *PythonAdapter) HandleDependencies(ctx context.Context, containerID string, deps *Dependencies) error {
	if deps == nil {
		return nil
	}

	var installCommands []string

	switch deps.Type {
	case DependencyTypePip:
		installCommands = pa.buildPipInstallCommands(deps)
	case DependencyTypeConda:
		installCommands = pa.buildCondaInstallCommands(deps)
	default:
		return fmt.Errorf("不支持的Python依赖管理器: %s", deps.Type)
	}

	// TODO: 在容器中执行安装命令
	pa.logger.Info("Python依赖安装命令", "commands", installCommands)

	return nil
}

// GetHealthCheckCommand 获取健康检查命令
func (pa *PythonAdapter) GetHealthCheckCommand() []string {
	return []string{"python", "--version"}
}

// GetDefaultEnvironment 获取默认环境变量
func (pa *PythonAdapter) GetDefaultEnvironment() map[string]string {
	env := pa.BaseRuntimeAdapter.GetDefaultEnvironment()
	
	// Python特定环境变量
	env["PYTHONUNBUFFERED"] = "1"
	env["PYTHONDONTWRITEBYTECODE"] = "1"
	env["PYTHONIOENCODING"] = "utf-8"
	env["PIP_NO_CACHE_DIR"] = "1"
	env["PIP_DISABLE_PIP_VERSION_CHECK"] = "1"
	
	return env
}

// ParseExecutionResult 解析执行结果
func (pa *PythonAdapter) ParseExecutionResult(output string, exitCode int) (*ExecutionResult, error) {
	result, err := pa.BaseRuntimeAdapter.ParseExecutionResult(output, exitCode)
	if err != nil {
		return nil, err
	}

	// Python特定的结果解析
	result.Metadata["runtime"] = "python"
	result.Metadata["version"] = pa.info.Version

	// 解析Python错误信息
	if exitCode != 0 {
		result.Metadata["error_type"] = pa.parseErrorType(output)
		result.Metadata["traceback"] = pa.extractTraceback(output)
	}

	return result, nil
}

// 私有方法

// buildEnvironment 构建环境变量
func (pa *PythonAdapter) buildEnvironment(req *ExecutionRequest, config *RuntimeConfig) map[string]string {
	// 获取默认环境变量
	env := pa.GetDefaultEnvironment()

	// 合并配置中的环境变量
	if config.Environment != nil {
		env = MergeEnvironment(env, config.Environment)
	}

	// 合并请求中的环境变量
	if req.Environment != nil {
		env = MergeEnvironment(env, req.Environment)
	}

	// 添加参数作为环境变量
	for key, value := range req.Parameters {
		envKey := fmt.Sprintf("PARAM_%s", strings.ToUpper(key))
		env[envKey] = fmt.Sprintf("%v", value)
	}

	return env
}

// buildPythonOptions 构建Python选项
func (pa *PythonAdapter) buildPythonOptions(options map[string]interface{}) []string {
	var pythonOptions []string

	// 优化模式
	if optimize, ok := options["optimize"]; ok && optimize.(bool) {
		pythonOptions = append(pythonOptions, "-O")
	}

	// 调试模式
	if debug, ok := options["debug"]; ok && debug.(bool) {
		pythonOptions = append(pythonOptions, "-d")
	}

	// 详细模式
	if verbose, ok := options["verbose"]; ok && verbose.(bool) {
		pythonOptions = append(pythonOptions, "-v")
	}

	// 警告控制
	if warnings, ok := options["warnings"]; ok {
		pythonOptions = append(pythonOptions, "-W", warnings.(string))
	}

	// 无缓冲输出
	if unbuffered, ok := options["unbuffered"]; ok && unbuffered.(bool) {
		pythonOptions = append(pythonOptions, "-u")
	}

	return pythonOptions
}

// buildDependencyInstallCommand 构建依赖安装命令
func (pa *PythonAdapter) buildDependencyInstallCommand(deps *Dependencies) []string {
	switch deps.Type {
	case DependencyTypePip:
		return pa.buildPipInstallCommands(deps)
	case DependencyTypeConda:
		return pa.buildCondaInstallCommands(deps)
	default:
		return []string{}
	}
}

// buildPipInstallCommands 构建pip安装命令
func (pa *PythonAdapter) buildPipInstallCommands(deps *Dependencies) []string {
	var commands []string

	// 升级pip
	commands = append(commands, "pip", "install", "--upgrade", "pip", "&&")

	// 设置pip配置
	if deps.Registry != "" {
		commands = append(commands, "pip", "config", "set", "global.index-url", deps.Registry, "&&")
	}

	// 安装依赖
	if deps.File != "" {
		// 从requirements.txt安装
		commands = append(commands, "pip", "install", "-r", deps.File)
		
		// 添加pip选项
		if deps.Options != nil {
			if noCache, ok := deps.Options["no_cache"]; ok && noCache.(bool) {
				commands = append(commands, "--no-cache-dir")
			}
			if userInstall, ok := deps.Options["user"]; ok && userInstall.(bool) {
				commands = append(commands, "--user")
			}
		}
	} else if len(deps.Packages) > 0 {
		// 安装指定包
		commands = append(commands, "pip", "install")
		commands = append(commands, deps.Packages...)
	}

	return commands
}

// buildCondaInstallCommands 构建conda安装命令
func (pa *PythonAdapter) buildCondaInstallCommands(deps *Dependencies) []string {
	var commands []string

	// 更新conda
	commands = append(commands, "conda", "update", "-n", "base", "-c", "defaults", "conda", "&&")

	// 设置conda配置
	if deps.Registry != "" {
		commands = append(commands, "conda", "config", "--add", "channels", deps.Registry, "&&")
	}

	// 安装依赖
	if deps.File != "" {
		// 从environment.yml安装
		commands = append(commands, "conda", "env", "update", "-f", deps.File)
	} else if len(deps.Packages) > 0 {
		// 安装指定包
		commands = append(commands, "conda", "install", "-y")
		commands = append(commands, deps.Packages...)
	}

	return commands
}

// parseErrorType 解析错误类型
func (pa *PythonAdapter) parseErrorType(output string) string {
	output = strings.ToLower(output)
	
	if strings.Contains(output, "syntaxerror") {
		return "syntax_error"
	}
	if strings.Contains(output, "nameerror") {
		return "name_error"
	}
	if strings.Contains(output, "typeerror") {
		return "type_error"
	}
	if strings.Contains(output, "valueerror") {
		return "value_error"
	}
	if strings.Contains(output, "indexerror") {
		return "index_error"
	}
	if strings.Contains(output, "keyerror") {
		return "key_error"
	}
	if strings.Contains(output, "attributeerror") {
		return "attribute_error"
	}
	if strings.Contains(output, "importerror") || strings.Contains(output, "modulenotfounderror") {
		return "import_error"
	}
	if strings.Contains(output, "filenotfounderror") {
		return "file_not_found"
	}
	if strings.Contains(output, "permissionerror") {
		return "permission_error"
	}
	if strings.Contains(output, "memoryerror") {
		return "memory_error"
	}
	
	return "unknown_error"
}

// extractTraceback 提取Python traceback信息
func (pa *PythonAdapter) extractTraceback(output string) string {
	lines := strings.Split(output, "\n")
	var traceback []string
	inTraceback := false
	
	for _, line := range lines {
		if strings.HasPrefix(line, "Traceback (most recent call last):") {
			inTraceback = true
		}
		
		if inTraceback {
			traceback = append(traceback, line)
		}
	}
	
	return strings.Join(traceback, "\n")
}

// PythonRequirements Python依赖需求
type PythonRequirements struct {
	Packages []PythonPackage `json:"packages"`
}

// PythonPackage Python包信息
type PythonPackage struct {
	Name    string `json:"name"`
	Version string `json:"version"`
	Extras  []string `json:"extras,omitempty"`
}

// ParseRequirementsTxt 解析requirements.txt文件
func (pa *PythonAdapter) ParseRequirementsTxt(content []byte) (*PythonRequirements, error) {
	requirements := &PythonRequirements{}
	lines := strings.Split(string(content), "\n")
	
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}
		
		// 解析包名和版本
		pkg := pa.parsePackageLine(line)
		if pkg != nil {
			requirements.Packages = append(requirements.Packages, *pkg)
		}
	}
	
	return requirements, nil
}

// parsePackageLine 解析包行
func (pa *PythonAdapter) parsePackageLine(line string) *PythonPackage {
	// TODO: 实现完整的requirements.txt解析
	// 支持 package==1.0.0, package>=1.0.0, package[extra] 等格式
	
	// 简单实现
	parts := strings.Split(line, "==")
	if len(parts) == 2 {
		return &PythonPackage{
			Name:    strings.TrimSpace(parts[0]),
			Version: strings.TrimSpace(parts[1]),
		}
	}
	
	return &PythonPackage{
		Name: strings.TrimSpace(line),
	}
}

// ValidateRequirements 验证Python依赖需求
func (pa *PythonAdapter) ValidateRequirements(requirements *PythonRequirements) error {
	for _, pkg := range requirements.Packages {
		if pkg.Name == "" {
			return fmt.Errorf("包名不能为空")
		}
		
		// 验证包名格式
		if err := pa.validatePackageName(pkg.Name); err != nil {
			return fmt.Errorf("无效的包名 '%s': %w", pkg.Name, err)
		}
		
		// 验证版本格式
		if pkg.Version != "" {
			if err := pa.validatePackageVersion(pkg.Version); err != nil {
				return fmt.Errorf("无效的版本 '%s' for package '%s': %w", pkg.Version, pkg.Name, err)
			}
		}
	}
	
	return nil
}

// validatePackageName 验证包名
func (pa *PythonAdapter) validatePackageName(name string) error {
	// TODO: 实现Python包名验证逻辑
	// 符合PEP 508规范
	return nil
}

// validatePackageVersion 验证包版本
func (pa *PythonAdapter) validatePackageVersion(version string) error {
	// TODO: 实现Python版本验证逻辑
	// 符合PEP 440规范
	return nil
}
