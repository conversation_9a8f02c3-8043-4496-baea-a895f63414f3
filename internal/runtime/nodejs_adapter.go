package runtime

import (
	"context"
	"encoding/json"
	"fmt"
	"path/filepath"
	"strings"

	"paas-platform/internal/container"
	"paas-platform/pkg/logger"
)

// NodeJSAdapter Node.js运行时适配器
type NodeJSAdapter struct {
	*BaseRuntimeAdapter
}

// NewNodeJSAdapter 创建Node.js适配器
func NewNodeJSAdapter(logger logger.Logger) RuntimeAdapter {
	info := RuntimeInfo{
		Type:        RuntimeTypeNodeJS,
		Name:        "Node.js",
		Version:     "18.17.0",
		Description: "Node.js JavaScript运行时环境",
		ImageTag:    "node:18.17.0-alpine",
		Extensions:  []string{".js", ".mjs", ".ts"},
		Commands:    []string{"node", "npm", "yarn"},
		Supported:   true,
	}

	return &NodeJSAdapter{
		BaseRuntimeAdapter: NewBaseRuntimeAdapter(logger, info),
	}
}

// BuildContainerSpec 构建容器规格
func (na *NodeJSAdapter) BuildContainerSpec(ctx context.Context, req *ExecutionRequest) (*container.ContainerSpec, error) {
	// 获取运行时配置
	config := req.RuntimeConfig
	if config == nil {
		config = &RuntimeConfig{
			Type:     RuntimeTypeNodeJS,
			ImageTag: na.info.ImageTag,
		}
	}

	// 解析资源限制
	resourceLimits, err := ParseResourceLimits(config.Resources)
	if err != nil {
		return nil, fmt.Errorf("解析资源限制失败: %w", err)
	}

	// 构建环境变量
	environment := na.buildEnvironment(req, config)

	// 构建执行命令
	command, err := na.BuildExecutionCommand(req)
	if err != nil {
		return nil, fmt.Errorf("构建执行命令失败: %w", err)
	}

	// 构建容器规格
	spec := &container.ContainerSpec{
		ImageTag:    config.ImageTag,
		Command:     command,
		WorkingDir:  req.WorkingDir,
		Environment: environment,
		Resources:   resourceLimits,
		Volumes: []container.VolumeMount{
			{
				Source:   req.WorkingDir,
				Target:   "/workspace",
				ReadOnly: false,
			},
		},
		NetworkMode: "bridge",
		Timeout:     req.Timeout,
		AutoRemove:  true,
	}

	return spec, nil
}

// BuildExecutionCommand 构建执行命令
func (na *NodeJSAdapter) BuildExecutionCommand(req *ExecutionRequest) ([]string, error) {
	var command []string

	// 检查是否需要安装依赖
	if req.RuntimeConfig != nil && req.RuntimeConfig.Dependencies != nil {
		command = append(command, na.buildDependencyInstallCommand(req.RuntimeConfig.Dependencies)...)
		command = append(command, "&&")
	}

	// 基础Node.js执行命令
	command = append(command, "node")

	// 添加Node.js特定选项
	if req.RuntimeConfig != nil && req.RuntimeConfig.Options != nil {
		nodeOptions := na.buildNodeOptions(req.RuntimeConfig.Options)
		command = append(command, nodeOptions...)
	}

	// 添加脚本路径
	command = append(command, req.ScriptPath)

	// 添加参数
	args := BuildParameterArgs(req.Parameters, ParameterStyleDoubleDash)
	command = append(command, args...)

	return command, nil
}

// HandleDependencies 处理依赖安装
func (na *NodeJSAdapter) HandleDependencies(ctx context.Context, containerID string, deps *Dependencies) error {
	if deps == nil {
		return nil
	}

	var installCommands []string

	switch deps.Type {
	case DependencyTypeNPM:
		installCommands = na.buildNPMInstallCommands(deps)
	case DependencyTypeYarn:
		installCommands = na.buildYarnInstallCommands(deps)
	default:
		return fmt.Errorf("不支持的Node.js依赖管理器: %s", deps.Type)
	}

	// TODO: 在容器中执行安装命令
	// 这里需要调用容器管理器的ExecuteCommand方法
	na.logger.Info("Node.js依赖安装命令", "commands", installCommands)

	return nil
}

// GetHealthCheckCommand 获取健康检查命令
func (na *NodeJSAdapter) GetHealthCheckCommand() []string {
	return []string{"node", "--version"}
}

// GetDefaultEnvironment 获取默认环境变量
func (na *NodeJSAdapter) GetDefaultEnvironment() map[string]string {
	env := na.BaseRuntimeAdapter.GetDefaultEnvironment()
	
	// Node.js特定环境变量
	env["NODE_ENV"] = "production"
	env["NPM_CONFIG_CACHE"] = "/tmp/.npm"
	env["NPM_CONFIG_PROGRESS"] = "false"
	env["NPM_CONFIG_LOGLEVEL"] = "warn"
	env["NODE_OPTIONS"] = "--max-old-space-size=512"
	
	return env
}

// ParseExecutionResult 解析执行结果
func (na *NodeJSAdapter) ParseExecutionResult(output string, exitCode int) (*ExecutionResult, error) {
	result, err := na.BaseRuntimeAdapter.ParseExecutionResult(output, exitCode)
	if err != nil {
		return nil, err
	}

	// Node.js特定的结果解析
	result.Metadata["runtime"] = "nodejs"
	result.Metadata["version"] = na.info.Version

	// 解析Node.js错误信息
	if exitCode != 0 {
		result.Metadata["error_type"] = na.parseErrorType(output)
	}

	// 解析性能信息
	if perfInfo := na.parsePerformanceInfo(output); perfInfo != nil {
		result.Metadata["performance"] = perfInfo
	}

	return result, nil
}

// 私有方法

// buildEnvironment 构建环境变量
func (na *NodeJSAdapter) buildEnvironment(req *ExecutionRequest, config *RuntimeConfig) map[string]string {
	// 获取默认环境变量
	env := na.GetDefaultEnvironment()

	// 合并配置中的环境变量
	if config.Environment != nil {
		env = MergeEnvironment(env, config.Environment)
	}

	// 合并请求中的环境变量
	if req.Environment != nil {
		env = MergeEnvironment(env, req.Environment)
	}

	// 添加参数作为环境变量（如果使用环境变量传递参数）
	for key, value := range req.Parameters {
		envKey := fmt.Sprintf("PARAM_%s", strings.ToUpper(key))
		env[envKey] = fmt.Sprintf("%v", value)
	}

	return env
}

// buildNodeOptions 构建Node.js选项
func (na *NodeJSAdapter) buildNodeOptions(options map[string]interface{}) []string {
	var nodeOptions []string

	// 内存限制
	if maxMemory, ok := options["max_memory"]; ok {
		nodeOptions = append(nodeOptions, fmt.Sprintf("--max-old-space-size=%v", maxMemory))
	}

	// 启用实验性功能
	if experimental, ok := options["experimental"]; ok && experimental.(bool) {
		nodeOptions = append(nodeOptions, "--experimental-modules")
	}

	// 调试模式
	if debug, ok := options["debug"]; ok && debug.(bool) {
		nodeOptions = append(nodeOptions, "--inspect=0.0.0.0:9229")
	}

	// 性能分析
	if profile, ok := options["profile"]; ok && profile.(bool) {
		nodeOptions = append(nodeOptions, "--prof")
	}

	// ES模块支持
	if esm, ok := options["esm"]; ok && esm.(bool) {
		nodeOptions = append(nodeOptions, "--input-type=module")
	}

	return nodeOptions
}

// buildDependencyInstallCommand 构建依赖安装命令
func (na *NodeJSAdapter) buildDependencyInstallCommand(deps *Dependencies) []string {
	switch deps.Type {
	case DependencyTypeNPM:
		return na.buildNPMInstallCommands(deps)
	case DependencyTypeYarn:
		return na.buildYarnInstallCommands(deps)
	default:
		return []string{}
	}
}

// buildNPMInstallCommands 构建NPM安装命令
func (na *NodeJSAdapter) buildNPMInstallCommands(deps *Dependencies) []string {
	var commands []string

	// 设置NPM配置
	if deps.Registry != "" {
		commands = append(commands, "npm", "config", "set", "registry", deps.Registry, "&&")
	}

	// 安装依赖
	if deps.File != "" {
		// 从package.json安装
		commands = append(commands, "npm", "install")
		
		// 添加NPM选项
		if deps.Options != nil {
			if production, ok := deps.Options["production"]; ok && production.(bool) {
				commands = append(commands, "--production")
			}
			if silent, ok := deps.Options["silent"]; ok && silent.(bool) {
				commands = append(commands, "--silent")
			}
		}
	} else if len(deps.Packages) > 0 {
		// 安装指定包
		commands = append(commands, "npm", "install")
		commands = append(commands, deps.Packages...)
	}

	return commands
}

// buildYarnInstallCommands 构建Yarn安装命令
func (na *NodeJSAdapter) buildYarnInstallCommands(deps *Dependencies) []string {
	var commands []string

	// 设置Yarn配置
	if deps.Registry != "" {
		commands = append(commands, "yarn", "config", "set", "registry", deps.Registry, "&&")
	}

	// 安装依赖
	if deps.File != "" {
		// 从package.json安装
		commands = append(commands, "yarn", "install")
		
		// 添加Yarn选项
		if deps.Options != nil {
			if production, ok := deps.Options["production"]; ok && production.(bool) {
				commands = append(commands, "--production")
			}
			if frozen, ok := deps.Options["frozen"]; ok && frozen.(bool) {
				commands = append(commands, "--frozen-lockfile")
			}
		}
	} else if len(deps.Packages) > 0 {
		// 安装指定包
		commands = append(commands, "yarn", "add")
		commands = append(commands, deps.Packages...)
	}

	return commands
}

// parseErrorType 解析错误类型
func (na *NodeJSAdapter) parseErrorType(output string) string {
	output = strings.ToLower(output)
	
	if strings.Contains(output, "syntaxerror") {
		return "syntax_error"
	}
	if strings.Contains(output, "referenceerror") {
		return "reference_error"
	}
	if strings.Contains(output, "typeerror") {
		return "type_error"
	}
	if strings.Contains(output, "rangeerror") {
		return "range_error"
	}
	if strings.Contains(output, "module not found") {
		return "module_not_found"
	}
	if strings.Contains(output, "permission denied") {
		return "permission_denied"
	}
	if strings.Contains(output, "out of memory") {
		return "out_of_memory"
	}
	
	return "unknown_error"
}

// parsePerformanceInfo 解析性能信息
func (na *NodeJSAdapter) parsePerformanceInfo(output string) map[string]interface{} {
	// TODO: 实现Node.js性能信息解析
	// 可以解析执行时间、内存使用等信息
	return nil
}

// NodeJSPackageInfo Node.js包信息
type NodeJSPackageInfo struct {
	Name            string                 `json:"name"`
	Version         string                 `json:"version"`
	Description     string                 `json:"description"`
	Main            string                 `json:"main"`
	Scripts         map[string]string      `json:"scripts"`
	Dependencies    map[string]string      `json:"dependencies"`
	DevDependencies map[string]string      `json:"devDependencies"`
	Engines         map[string]string      `json:"engines"`
	Keywords        []string               `json:"keywords"`
	Author          string                 `json:"author"`
	License         string                 `json:"license"`
	Repository      map[string]interface{} `json:"repository"`
}

// ParsePackageJSON 解析package.json文件
func (na *NodeJSAdapter) ParsePackageJSON(content []byte) (*NodeJSPackageInfo, error) {
	var packageInfo NodeJSPackageInfo
	if err := json.Unmarshal(content, &packageInfo); err != nil {
		return nil, fmt.Errorf("解析package.json失败: %w", err)
	}
	return &packageInfo, nil
}

// ValidatePackageJSON 验证package.json配置
func (na *NodeJSAdapter) ValidatePackageJSON(packageInfo *NodeJSPackageInfo) error {
	if packageInfo.Name == "" {
		return fmt.Errorf("package.json中缺少name字段")
	}
	
	if packageInfo.Version == "" {
		return fmt.Errorf("package.json中缺少version字段")
	}
	
	// 验证Node.js版本要求
	if nodeVersion, ok := packageInfo.Engines["node"]; ok {
		if err := na.validateNodeVersion(nodeVersion); err != nil {
			return fmt.Errorf("Node.js版本要求不满足: %w", err)
		}
	}
	
	return nil
}

// validateNodeVersion 验证Node.js版本
func (na *NodeJSAdapter) validateNodeVersion(requiredVersion string) error {
	// TODO: 实现版本比较逻辑
	// 可以使用semver库进行版本比较
	return nil
}

// GetScriptCommands 获取package.json中的脚本命令
func (na *NodeJSAdapter) GetScriptCommands(packageInfo *NodeJSPackageInfo) map[string]string {
	if packageInfo.Scripts == nil {
		return make(map[string]string)
	}
	return packageInfo.Scripts
}

// BuildScriptCommand 构建npm script命令
func (na *NodeJSAdapter) BuildScriptCommand(scriptName string, packageInfo *NodeJSPackageInfo) ([]string, error) {
	if packageInfo.Scripts == nil {
		return nil, fmt.Errorf("package.json中没有定义scripts")
	}
	
	script, exists := packageInfo.Scripts[scriptName]
	if !exists {
		return nil, fmt.Errorf("script '%s' 不存在", scriptName)
	}
	
	// 构建npm run命令
	return []string{"npm", "run", scriptName}, nil
}

// DetectPackageManager 检测包管理器类型
func (na *NodeJSAdapter) DetectPackageManager(workingDir string) DependencyType {
	// 检查yarn.lock文件
	if na.fileExists(filepath.Join(workingDir, "yarn.lock")) {
		return DependencyTypeYarn
	}
	
	// 检查package-lock.json文件
	if na.fileExists(filepath.Join(workingDir, "package-lock.json")) {
		return DependencyTypeNPM
	}
	
	// 默认使用npm
	return DependencyTypeNPM
}

// fileExists 检查文件是否存在
func (na *NodeJSAdapter) fileExists(filename string) bool {
	// TODO: 实现文件存在检查
	return false
}
