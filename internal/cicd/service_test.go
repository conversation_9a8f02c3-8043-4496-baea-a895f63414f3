package cicd

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// MockLogger 模拟日志记录器
type MockLogger struct {
	mock.Mock
}

func (m *MockLogger) Debug(msg string, keysAndValues ...interface{}) {
	m.Called(msg, keysAndValues)
}

func (m *<PERSON>ckLogger) Info(msg string, keysAndValues ...interface{}) {
	m.Called(msg, keysAndValues)
}

func (m *MockLogger) Warn(msg string, keysAndValues ...interface{}) {
	m.Called(msg, keysAndValues)
}

func (m *MockLogger) Error(msg string, keysAndValues ...interface{}) {
	m.Called(msg, keysAndValues)
}

// setupTestDB 设置测试数据库
func setupTestDB(t *testing.T) *gorm.DB {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	assert.NoError(t, err)
	
	// 自动迁移测试表
	err = db.AutoMigrate(
		&Pipeline{},
		&Build{},
		&BuildStage{},
		&BuildStep{},
		&Webhook{},
		&BuildNode{},
		&BuildQueue{},
		&Deployment{},
		&Environment{},
	)
	assert.NoError(t, err)
	
	return db
}

// TestCICDService_CreatePipeline 测试创建流水线
func TestCICDService_CreatePipeline(t *testing.T) {
	db := setupTestDB(t)
	logger := &MockLogger{}
	service := NewCICDService(db, logger)
	
	ctx := context.Background()
	
	// 准备测试数据
	req := &CreatePipelineRequest{
		Name:        "测试流水线",
		Description: "这是一个测试流水线",
		AppID:       "app-123",
		Config: PipelineConfig{
			APIVersion: "v1",
			Kind:       "Pipeline",
			Metadata: PipelineMetadata{
				Name:        "test-pipeline",
				Description: "测试流水线配置",
			},
			Spec: PipelineSpec{
				Triggers: []Trigger{
					{
						Type:     "push",
						Branches: []string{"main", "develop"},
					},
				},
				Stages: []Stage{
					{
						Name: "构建",
						Steps: []Step{
							{
								Name: "检出代码",
								Type: "git_checkout",
								Config: StepConfig{
									"repository": "https://github.com/example/repo.git",
								},
							},
							{
								Name: "构建应用",
								Type: "shell",
								Config: map[string]interface{}{
									"commands": []string{"npm install", "npm run build"},
								},
							},
						},
					},
				},
			},
		},
	}
	
	// 设置 mock 期望
	logger.On("Info", mock.AnythingOfType("string"), mock.Anything).Return()
	
	// 执行测试
	pipeline, err := service.CreatePipeline(ctx, req)
	
	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, pipeline)
	assert.Equal(t, req.Name, pipeline.Name)
	assert.Equal(t, req.Description, pipeline.Description)
	assert.Equal(t, req.AppID, pipeline.AppID)
	assert.Equal(t, PipelineStatusActive, pipeline.Status)
	assert.NotEmpty(t, pipeline.ID)
	
	// 验证数据库中的记录
	var dbPipeline Pipeline
	err = db.First(&dbPipeline, "id = ?", pipeline.ID).Error
	assert.NoError(t, err)
	assert.Equal(t, pipeline.Name, dbPipeline.Name)
	
	// 验证 mock 调用
	logger.AssertExpectations(t)
}

// TestCICDService_CreateBuild 测试创建构建
func TestCICDService_CreateBuild(t *testing.T) {
	db := setupTestDB(t)
	logger := &MockLogger{}
	service := NewCICDService(db, logger)
	
	ctx := context.Background()
	
	// 先创建一个流水线
	pipeline := &Pipeline{
		ID:          "pipeline-123",
		Name:        "测试流水线",
		Description: "测试流水线描述",
		AppID:       "app-123",
		Status:      PipelineStatusActive,
		Config: PipelineConfig{
			APIVersion: "v1",
			Kind:       "Pipeline",
			Spec: PipelineSpec{
				Stages: []Stage{
					{
						Name: "构建",
						Steps: []Step{
							{
								Name: "检出代码",
								Type: "git_checkout",
							},
						},
					},
				},
			},
		},
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
	err := db.Create(pipeline).Error
	assert.NoError(t, err)
	
	// 准备构建请求
	req := &CreateBuildRequest{
		PipelineID:  pipeline.ID,
		Branch:      "main",
		CommitHash:  "abc123def456",
		CommitMsg:   "测试提交",
		Author:      "test-user",
		TriggerType: "manual",
		Priority:    1,
	}
	
	// 设置 mock 期望
	logger.On("Info", mock.AnythingOfType("string"), mock.Anything).Return()
	
	// 执行测试
	build, err := service.CreateBuild(ctx, req)
	
	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, build)
	assert.Equal(t, req.PipelineID, build.PipelineID)
	assert.Equal(t, req.Branch, build.Branch)
	assert.Equal(t, req.CommitHash, build.CommitHash)
	assert.Equal(t, req.CommitMsg, build.CommitMsg)
	assert.Equal(t, req.Author, build.Author)
	assert.Equal(t, BuildStatusPending, build.Status)
	assert.NotEmpty(t, build.ID)
	assert.Greater(t, build.Number, 0)
	
	// 验证构建阶段和步骤是否创建
	var stages []BuildStage
	err = db.Where("build_id = ?", build.ID).Find(&stages).Error
	assert.NoError(t, err)
	assert.Len(t, stages, 1)
	
	var steps []BuildStep
	err = db.Where("stage_id = ?", stages[0].ID).Find(&steps).Error
	assert.NoError(t, err)
	assert.Len(t, steps, 1)
	
	// 验证 mock 调用
	logger.AssertExpectations(t)
}

// TestCICDService_UpdateBuildStatus 测试更新构建状态
func TestCICDService_UpdateBuildStatus(t *testing.T) {
	db := setupTestDB(t)
	logger := &MockLogger{}
	service := NewCICDService(db, logger)
	
	ctx := context.Background()
	
	// 创建测试构建
	build := &Build{
		ID:         "build-123",
		PipelineID: "pipeline-123",
		Number:     1,
		Status:     BuildStatusPending,
		Branch:     "main",
		CommitHash: "abc123",
		StartTime:  time.Now(),
	}
	err := db.Create(build).Error
	assert.NoError(t, err)
	
	// 设置 mock 期望
	logger.On("Info", mock.AnythingOfType("string"), mock.Anything).Return()
	
	// 执行测试
	err = service.UpdateBuildStatus(ctx, build.ID, BuildStatusRunning, "构建开始运行")
	
	// 验证结果
	assert.NoError(t, err)
	
	// 验证数据库中的状态更新
	var updatedBuild Build
	err = db.First(&updatedBuild, "id = ?", build.ID).Error
	assert.NoError(t, err)
	assert.Equal(t, BuildStatusRunning, updatedBuild.Status)
	assert.NotNil(t, updatedBuild.StartTime)
	
	// 验证 mock 调用
	logger.AssertExpectations(t)
}

// TestCICDService_GetBuild 测试获取构建详情
func TestCICDService_GetBuild(t *testing.T) {
	db := setupTestDB(t)
	logger := &MockLogger{}
	service := NewCICDService(db, logger)
	
	ctx := context.Background()
	
	// 创建测试数据
	build := &Build{
		ID:         "build-123",
		PipelineID: "pipeline-123",
		Number:     1,
		Status:     BuildStatusSuccess,
		Branch:     "main",
		CommitHash: "abc123",
		StartTime:  time.Now(),
	}
	err := db.Create(build).Error
	assert.NoError(t, err)
	
	// 创建构建阶段
	stage := &BuildStage{
		ID:      "stage-123",
		BuildID: build.ID,
		Name:    "构建阶段",
		Status:  BuildStageStatusSuccess,
		Order:   1,
	}
	err = db.Create(stage).Error
	assert.NoError(t, err)
	
	// 创建构建步骤
	step := &BuildStep{
		ID:      "step-123",
		StageID: stage.ID,
		Name:    "构建步骤",
		Type:    "shell",
		Status:  BuildStepStatusSuccess,
		Order:   1,
	}
	err = db.Create(step).Error
	assert.NoError(t, err)
	
	// 执行测试
	result, err := service.GetBuild(ctx, build.ID)
	
	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, build.ID, result.ID)
	assert.Equal(t, build.Status, result.Status)
	assert.Len(t, result.Stages, 1)
	assert.Equal(t, stage.Name, result.Stages[0].Name)
	assert.Len(t, result.Stages[0].Steps, 1)
	assert.Equal(t, step.Name, result.Stages[0].Steps[0].Name)
}

// TestCICDService_ListBuilds 测试获取构建列表
func TestCICDService_ListBuilds(t *testing.T) {
	db := setupTestDB(t)
	logger := &MockLogger{}
	service := NewCICDService(db, logger)
	
	ctx := context.Background()
	
	// 创建测试数据
	pipelineID := "pipeline-123"
	builds := []*Build{
		{
			ID:         "build-1",
			PipelineID: pipelineID,
			Number:     1,
			Status:     BuildStatusSuccess,
			Branch:     "main",
			StartTime:  time.Now().Add(-2 * time.Hour),
		},
		{
			ID:         "build-2",
			PipelineID: pipelineID,
			Number:     2,
			Status:     BuildStatusFailed,
			Branch:     "develop",
			StartTime:  time.Now().Add(-1 * time.Hour),
		},
		{
			ID:         "build-3",
			PipelineID: "other-pipeline",
			Number:     1,
			Status:     BuildStatusSuccess,
			Branch:     "main",
			StartTime:  time.Now(),
		},
	}
	
	for _, build := range builds {
		err := db.Create(build).Error
		assert.NoError(t, err)
	}
	
	// 测试过滤条件
	filter := &BuildFilter{
		PipelineID: pipelineID,
		Page:       1,
		PageSize:   10,
	}
	
	// 执行测试
	result, total, err := service.ListBuilds(ctx, filter)
	
	// 验证结果
	assert.NoError(t, err)
	assert.Equal(t, int64(2), total) // 只有2个构建属于指定流水线
	assert.Len(t, result, 2)
	
	// 验证排序（按创建时间倒序）
	assert.Equal(t, "build-2", result[0].ID) // 最新的构建
	assert.Equal(t, "build-1", result[1].ID)
}

// TestCICDService_CancelBuild 测试取消构建
func TestCICDService_CancelBuild(t *testing.T) {
	db := setupTestDB(t)
	logger := &MockLogger{}
	service := NewCICDService(db, logger)
	
	ctx := context.Background()
	
	// 创建运行中的构建
	build := &Build{
		ID:         "build-123",
		PipelineID: "pipeline-123",
		Number:     1,
		Status:     BuildStatusRunning,
		Branch:     "main",
		StartTime:  time.Now(),
	}
	err := db.Create(build).Error
	assert.NoError(t, err)
	
	// 设置 mock 期望
	logger.On("Info", mock.AnythingOfType("string"), mock.Anything).Return()
	
	// 执行测试
	err = service.CancelBuild(ctx, build.ID)
	
	// 验证结果
	assert.NoError(t, err)
	
	// 验证构建状态更新
	var updatedBuild Build
	err = db.First(&updatedBuild, "id = ?", build.ID).Error
	assert.NoError(t, err)
	assert.Equal(t, BuildStatusCanceled, updatedBuild.Status)
	assert.NotNil(t, updatedBuild.EndTime)
	
	// 验证 mock 调用
	logger.AssertExpectations(t)
}

// TestPipelineConfig_Validate 测试流水线配置验证
func TestPipelineConfig_Validate(t *testing.T) {
	tests := []struct {
		name    string
		config  PipelineConfig
		wantErr bool
	}{
		{
			name: "有效配置",
			config: PipelineConfig{
				APIVersion: "v1",
				Kind:       "Pipeline",
				Metadata: PipelineMetadata{
					Name: "test-pipeline",
				},
				Spec: PipelineSpec{
					Stages: []Stage{
						{
							Name: "构建",
							Steps: []Step{
								{
									Name: "检出代码",
									Type: "git_checkout",
								},
							},
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "缺少API版本",
			config: PipelineConfig{
				Kind: "Pipeline",
				Metadata: PipelineMetadata{
					Name: "test-pipeline",
				},
			},
			wantErr: true,
		},
		{
			name: "缺少阶段",
			config: PipelineConfig{
				APIVersion: "v1",
				Kind:       "Pipeline",
				Metadata: PipelineMetadata{
					Name: "test-pipeline",
				},
				Spec: PipelineSpec{
					Stages: []Stage{},
				},
			},
			wantErr: true,
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.Validate()
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// TestCreateBuildRequest_Validate 测试创建构建请求验证
func TestCreateBuildRequest_Validate(t *testing.T) {
	tests := []struct {
		name    string
		req     CreateBuildRequest
		wantErr bool
	}{
		{
			name: "有效请求",
			req: CreateBuildRequest{
				PipelineID:  "pipeline-123",
				Branch:      "main",
				CommitHash:  "abc123def456",
				CommitMsg:   "测试提交",
				Author:      "test-user",
				TriggerType: "manual",
			},
			wantErr: false,
		},
		{
			name: "缺少流水线ID",
			req: CreateBuildRequest{
				Branch:      "main",
				CommitHash:  "abc123def456",
				TriggerType: "manual",
			},
			wantErr: true,
		},
		{
			name: "缺少分支",
			req: CreateBuildRequest{
				PipelineID:  "pipeline-123",
				CommitHash:  "abc123def456",
				TriggerType: "manual",
			},
			wantErr: true,
		},
		{
			name: "无效的触发类型",
			req: CreateBuildRequest{
				PipelineID:  "pipeline-123",
				Branch:      "main",
				CommitHash:  "abc123def456",
				TriggerType: "invalid",
			},
			wantErr: true,
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.req.Validate()
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
